{"name": "adminmodule", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "lint": "eslint .", "preview": "vite preview", "format:write": "prettier --write .", "format:check": "prettier --check .", "test": "vitest run", "test:watch": "vitest", "build": "tsc -p tsconfig.build.json && vite build", "prepare": "husky"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint  --no-warn-ignored --max-warnings 0"], "*.md": ["prettier --write"]}, "dependencies": {"@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@react-buddy/ide-toolbox": "^2.4.0", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.0.12", "axios": "^1.8.3", "daisyui": "^5.0.0", "framer-motion": "^12.7.3", "lucide-react": "^0.487.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hooks": "^1.0.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-refresh": "^0.16.0", "react-responsive-carousel": "^3.2.23", "react-router": "^7.3.0", "swr": "^2.3.3", "tailwindcss": "^4.0.12", "zaplog": "^1.2.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.13.10", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.2.0", "vitest": "^3.0.8"}}