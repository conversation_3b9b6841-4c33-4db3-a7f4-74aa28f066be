import React, { useEffect, useState } from 'react';
import { Menu } from '@headlessui/react';
import PaymentModal from '../utils/PaymentModal';
import InvoiceModal from '../utils/invoiceModal';

function PaymentRequests() {
    const [invoices, setInvoices] = useState([]);
    const [filterStatus, setFilterStatus] = useState('');
    const [searchQuery, setSearchQuery] = useState('');

    const refreshPurchasedPackages = () => {
        fetch("http://192.168.100.14/EBridge/api/TssEBridge/GetPurchasedPackagesBySAdmin", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                userName: localStorage.getItem("email"),
                sessionKey: localStorage.getItem("sessionKey"),
            }),
        })
            .then(res => res.json())
            .then(data => {
                if (data.valid) setInvoices(data.data);
                else console.error("Failed to fetch packages:", data.message);
            })
            .catch(err => console.error("Error fetching data:", err));
    };

    useEffect(() => {
        refreshPurchasedPackages();
    }, []);

    const filteredInvoices = invoices.filter(invoice => {
        const statusMatch = !filterStatus || invoice.status === filterStatus;
        const searchMatch = searchQuery === '' ||
            invoice.organizationName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            invoice.phoneNumber.toLowerCase().includes(searchQuery.toLowerCase());
        return statusMatch && searchMatch;
    });

    return (
        <div className="w-full p-4 md:p-6">
            {/* Summary Cards */}
            <div className="flex flex-wrap gap-4 mb-6 overflow-x-auto">
                <div className="card w-full sm:w-52 bg-success text-success-content">
                    <div className="card-body items-center text-center">
                        <p>Approved Invoices</p>
                        <h2 className="card-title text-2xl">15</h2>
                    </div>
                </div>
                <div className="card w-full sm:w-52 bg-warning text-warning-content">
                    <div className="card-body items-center text-center">
                        <p>Pending Invoices</p>
                        <h2 className="card-title text-2xl">60</h2>
                    </div>
                </div>
                <div className="card w-full sm:w-52 bg-error text-error-content">
                    <div className="card-body items-center text-center">
                        <p>Declined Invoices</p>
                        <h2 className="card-title text-2xl">15</h2>
                    </div>
                </div>
            </div>

            {/* Search & Filter */}
            <div className="flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center mb-4">
                <input
                    type="text"
                    className="input input-bordered w-full sm:max-w-xs"
                    placeholder="Search by name or contact..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                />

                <Menu as="div" className="relative inline-block text-left">
                    <Menu.Button className="btn btn-outline">
                        Filter by Status
                        <svg className="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                    </Menu.Button>
                    <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-base-100 border border-base-300 shadow-lg focus:outline-none">
                        {["", "Verified", "In Process", "Pending"].map(status => (
                            <Menu.Item key={status}>
                                {({ active }) => (
                                    <button
                                        className={`block w-full px-4 py-2 text-left ${active ? 'bg-base-200' : ''} ${filterStatus === status ? 'bg-primary text-primary-content' : ''}`}
                                        onClick={() => setFilterStatus(status)}
                                    >
                                        {status === '' ? 'All' : status}
                                    </button>
                                )}
                            </Menu.Item>
                        ))}
                    </Menu.Items>
                </Menu>
            </div>

            {/* Table */}
            <div className="overflow-x-auto border border-base-300 rounded-lg shadow">
                <table className="table w-full text-sm">
                    <thead className="bg-primary text-primary-content text-xs uppercase">
                        <tr>
                            <th>Purchase ID</th>
                            <th>Name</th>
                            <th>Contact</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {filteredInvoices.map((invoice) => (
                            <tr key={invoice.id} className="hover:bg-base-200">
                                <td>{invoice.purchaseID}</td>
                                <td>
                                    <div className="flex items-center gap-3">
                                        <div className="avatar">
                                            <div className="w-10 rounded-full">
                                                <img src="https://avatars.githubusercontent.com/u/68427058?v=4" alt="Avatar" />
                                            </div>
                                        </div>
                                        <div>
                                            <div className="font-bold">{invoice.organizationName}</div>
                                            <div className="text-sm opacity-70">{invoice.purchaseDate}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>{invoice.phoneNumber}</td>
                                <td>{invoice.totalAmount} PKR</td>
                                <td>
                                    <span className={`badge 
                                        ${invoice.status === 'Verified' ? 'badge-success' :
                                            invoice.status === 'Pending' ? 'badge-warning' :
                                                invoice.status === 'In Process' ? 'badge-info' : 'badge-neutral'}
                                    `}>
                                        {invoice.status}
                                    </span>
                                </td>
                                <td className="flex flex-wrap gap-2">
                                    <InvoiceModal />
                                    {invoice.status === 'In Process' && (
                                        <PaymentModal
                                            purchaseID={invoice.purchaseID}
                                            note="this is purchase note"
                                            proof="https://channelx.world/wp-content/uploads/2022/11/PayPal-Invoicing-1024x683.jpg"
                                            amount={invoice.totalAmount}
                                            status={invoice.status}
                                            refreshPurchasedPackages={refreshPurchasedPackages}
                                        />
                                    )}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}

export default PaymentRequests;
