import { useEffect, useState } from "react";
import { NavLink } from "react-router";
import {
  BuildingLibraryIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ArchiveBoxIcon,
  ClockIcon,
} from "@heroicons/react/24/solid";
import { notify } from "@/lib/notify";
import { logger } from "@/lib/logger";

// Component for Package Card
const PackageCard = ({ packageData }) => {
  const { title, price, branches, students } = packageData;
  return (
    <div className="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
      <div className="card-body">
        <h3 className="card-title text-lg font-bold">{title}</h3>
        <div className="mt-2">
          <p className="text-2xl font-bold text-primary">
            {price?.toLocaleString()} PKR
            <span className="text-sm text-base-content/70">/month</span>
          </p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">✓ {branches} Branches</p>
            <p className="text-sm">✓ {students} Students</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Component for Owner Card
const OwnerCard = ({ ownerData }) => {
  const { name, email, phone } = ownerData;
  return (
    <div className="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
      <div className="card-body">
        <h3 className="card-title text-lg font-bold">{name}</h3>
        <div className="mt-2 space-y-1">
          <p className="text-sm flex items-center gap-2">
            <span className="text-primary">✉</span> {email}
          </p>
          <p className="text-sm flex items-center gap-2">
            <span className="text-primary">☎</span> {phone}
          </p>
        </div>
      </div>
    </div>
  );
};

// Component for Subscription Card
const SubscriptionCard = ({ subscription, owner, package: pkg }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "text-success";
      case "pending":
        return "text-warning";
      case "overdue":
        return "text-error";
      default:
        return "text-base-content";
    }
  };

  return (
    <div className="card bg-base-100 shadow-lg hover:shadow-xl transition-all">
      <div className="card-body">
        <div className="flex items-center justify-between">
          <h3 className="card-title text-lg font-bold">{owner?.name}</h3>
          <span
            className={`badge ${getStatusColor(subscription.paymentStatus)}`}
          >
            {subscription.paymentStatus}
          </span>
        </div>
        <div className="mt-2 space-y-2">
          <p className="text-sm font-medium">{pkg?.title}</p>
          <p className="text-sm flex items-center gap-2">
            <ClockIcon className="h-4 w-4 text-primary" />
            {new Date(subscription.startDate).toLocaleDateString()} -{" "}
            {new Date(subscription.endDate).toLocaleDateString()}
          </p>
          <p className="text-sm font-semibold text-primary">
            {pkg?.price?.toLocaleString()} PKR/month
          </p>
        </div>
      </div>
    </div>
  );
};

function Dashboard() {
  const [packages, setPackages] = useState([]);
  const [owners, setOwners] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [packagesData, ownersData, subscriptionsData] = await Promise.all(
          [fetchAllPackages(), fetchAllOwners(), fetchPackageSubscriptions()]
        );

        setPackages(packagesData || []);
        setOwners(ownersData || []);
        setSubscriptions(subscriptionsData || []);
        notify.success("Dashboard data loaded successfully!");
      } catch (error) {
        logger.error("Error fetching dashboard data:", error);
        notify.error("Failed to load some dashboard data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Calculate statistics
  const stats = [
    {
      title: "Total Packages",
      value: packages.length,
      bgColor: "bg-primary",
      icon: <ArchiveBoxIcon className="h-10 w-10 text-white" />,
      path: "/code/packages",
    },
    {
      title: "Total Owners",
      value: owners.length,
      bgColor: "bg-secondary",
      icon: <UserGroupIcon className="h-10 w-10 text-white" />,
      path: "/code/owners",
    },
    {
      title: "Active Subscriptions",
      value: subscriptions.filter((sub) => sub.status === "active").length,
      bgColor: "bg-success",
      icon: <CheckCircleIcon className="h-10 w-10 text-white" />,
      path: "/code/owners",
    },
    {
      title: "Payment Due",
      value: subscriptions.filter((sub) => sub.paymentStatus === "overdue")
        .length,
      bgColor: "bg-warning",
      icon: <CurrencyDollarIcon className="h-10 w-10 text-white" />,
      path: "/accounts/payment-requests",
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[60vh]">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Section */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <NavLink
            key={index}
            to={stat.path}
            className={`${stat.bgColor} rounded-2xl shadow-lg p-6 text-white transition-transform hover:scale-105`}
          >
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0">{stat.icon}</div>
              <div>
                <h4 className="text-lg font-semibold">{stat.title}</h4>
                <p className="text-2xl font-bold">{stat.value}</p>
              </div>
            </div>
          </NavLink>
        ))}
      </div>

      {/* Active Subscriptions Section */}
      <div className="card bg-base-200 shadow-lg">
        <div className="card-body">
          <h2 className="card-title text-xl mb-4">Active Subscriptions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {subscriptions
              .filter((sub) => sub.status === "active")
              .slice(0, 3)
              .map((subscription, index) => {
                const owner = owners.find((o) => o.id === subscription.ownerId);
                const pkg = packages.find(
                  (p) => p.id === subscription.packageId
                );
                return (
                  <SubscriptionCard
                    key={index}
                    subscription={subscription}
                    owner={owner}
                    package={pkg}
                  />
                );
              })}
          </div>
        </div>
      </div>

      {/* Recent Packages Section */}
      <div className="card bg-base-200 shadow-lg">
        <div className="card-body">
          <h2 className="card-title text-xl mb-4">Recent Packages</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {packages.slice(0, 3).map((pkg, index) => (
              <PackageCard key={index} packageData={pkg} />
            ))}
          </div>
          {packages.length > 3 && (
            <div className="mt-4 text-center">
              <NavLink to="/code/packages" className="btn btn-primary btn-sm">
                View All Packages
              </NavLink>
            </div>
          )}
        </div>
      </div>

      {/* Recent Owners Section */}
      <div className="card bg-base-200 shadow-lg">
        <div className="card-body">
          <h2 className="card-title text-xl mb-4">Recent Owners</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {owners.slice(0, 3).map((owner, index) => (
              <OwnerCard key={index} ownerData={owner} />
            ))}
          </div>
          {owners.length > 3 && (
            <div className="mt-4 text-center">
              <NavLink to="/code/owners" className="btn btn-primary btn-sm">
                View All Owners
              </NavLink>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
