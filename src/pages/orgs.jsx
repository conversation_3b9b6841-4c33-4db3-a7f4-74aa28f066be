import StatsModal from '../utils/orgModal';

function Orgs() {
    const OrgsList = [
        { id: 1, name: 'Govt. Girls Degree College Qalandarabad, Abbottabad', package: "Monthly Plus", status: 'Pending' },
        { id: 2, name: 'Govt. Boys Degree College Qalandarabad, Abbottabad', package: "Annual Pro", status: 'Subscribe' },
        { id: 3, name: 'Govt. Degree College Qalandarabad, Abbottabad', package: "Trial (1 Month)", status: 'Not Subscribe' },
    ];

    return (
        <div className="w-full p-4 md:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                <h1 className="text-xl font-semibold text-base-content">Organizations</h1>
                <input
                    type="text"
                    placeholder="Search organizations..."
                    className="input input-bordered w-full sm:max-w-xs"
                />
            </div>

            {/* Table */}
            <div className="overflow-x-auto border border-base-300 rounded-lg shadow">
                <table className="table w-full text-sm">
                    <thead className="bg-primary text-primary-content text-xs uppercase">
                        <tr>
                            <th>Name</th>
                            <th className="text-center">Campus</th>
                            <th className="text-center">Package</th>
                            <th className="text-center">Status</th>
                            <th className="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {OrgsList.map((org) => (
                            <tr key={org.id} className="hover:bg-base-200">
                                {/* Name with avatar - left aligned */}
                                <td>
                                    <div className="flex items-center gap-3">
                                        <div className="avatar">
                                            <div className="w-10 rounded-full">
                                                <img src="https://avatars.githubusercontent.com/u/68427058?v=4" alt="Avatar" />
                                            </div>
                                        </div>
                                        <div>
                                            <div className="font-bold">{org.name}</div>
                                            <div className="text-sm opacity-70">+923165502120</div>
                                        </div>
                                    </div>
                                </td>

                                {/* Campus - centered */}
                                <td className="text-center">3</td>

                                {/* Package - centered */}
                                <td className="text-center">{org.package}</td>

                                {/* Status with badge and dot - centered */}
                                <td className="text-center">
                                    <div className="flex items-center justify-center gap-2">
                                        <span
                                            className={`h-2 w-2 rounded-full 
                                                ${org.status === 'Subscribe' ? 'bg-green-600' :
                                                    org.status === 'Not Subscribe' ? 'bg-red-600' :
                                                        org.status === 'Pending' ? 'bg-yellow-400' :
                                                            'bg-neutral'}`}
                                        ></span>
                                        <span
                                            className={`badge badge-sm 
                                                ${org.status === 'Subscribe' ? 'badge-success' :
                                                    org.status === 'Not Subscribe' ? 'badge-error' :
                                                        org.status === 'Pending' ? 'badge-warning' :
                                                            'badge-neutral'}`}
                                        >
                                            {org.status}
                                        </span>
                                    </div>
                                </td>

                                {/* Modal Button - centered */}
                                <td className="text-center">
                                    <div className="flex justify-center">
                                        <StatsModal name={org.name} />
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}

export default Orgs;