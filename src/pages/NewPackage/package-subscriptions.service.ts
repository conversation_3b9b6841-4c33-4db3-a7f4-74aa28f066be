/* eslint-disable @typescript-eslint/require-await */

// Types for package subscriptions
export interface PackageSubscription {
  id: string;
  ownerId: string;
  packageId: string;
  startDate: Date;
  endDate: Date;
  status: "active" | "expired" | "cancelled";
  paymentStatus: "paid" | "pending" | "overdue";
}

// Dummy data for development
const dummySubscriptions: PackageSubscription[] = [
  {
    id: "sub1",
    ownerId: "owner1",
    packageId: "pkg1",
    startDate: new Date(2025, 0, 1),
    endDate: new Date(2025, 11, 31),
    status: "active",
    paymentStatus: "paid",
  },
  {
    id: "sub2",
    ownerId: "owner2",
    packageId: "pkg2",
    startDate: new Date(2025, 1, 1),
    endDate: new Date(2025, 12, 31),
    status: "active",
    paymentStatus: "pending",
  },
  {
    id: "sub3",
    ownerId: "owner3",
    packageId: "pkg3",
    startDate: new Date(2025, 2, 1),
    endDate: new Date(2025, 11, 30),
    status: "active",
    paymentStatus: "overdue",
  },
];

export const fetchPackageSubscriptions = async () => {
  // In development, return dummy data
  // In production, uncomment the API call below
  return dummySubscriptions;

  // const response = await sendApiRequest("/package-subscriptions", {
  //   method: "GET",
  //   withAuthorization: true,
  // });
  // return response;
};
