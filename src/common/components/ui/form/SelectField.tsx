import type { SelectHTMLAttributes } from "react";
import type {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>P<PERSON>,
  <PERSON>V<PERSON><PERSON>,
  UseFormRegister,
} from "react-hook-form";

type Props<T extends FieldValues> = {
  name: FieldPath<T>;
  label: string;
  errorMessage?: FieldError["message"];
  register?: UseFormRegister<T>;
  options: { value: string | number; label: string; selected?: boolean }[];
  valueAsNumber?: boolean;
  className?: string;
} & SelectHTMLAttributes<HTMLSelectElement>;

export const SelectField = <T extends FieldValues>({
  name,
  label,
  errorMessage,
  register,
  options,
  valueAsNumber,
  className = "relative left-0.5",
  ...rest
}: Props<T>) => {
  return (
    <div className={`form-control w-full ${className}`}>
      {/* Label */}
      <label className="label font-medium">
        <span className="label-text text-base-content ">{label}</span>
      </label>
      <div className="relative">
        <select
          className={`select w-full bg-base-100 text-base-content focus:outline-none focus:ring-2 focus:ring-info ${errorMessage ? "border-red-500" : ""}`}
          {...(register ? register(name, { valueAsNumber }) : {})}
          {...rest}
        >
          <option value="select" disabled>
            Select
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
      {errorMessage && (
        <span className="text-error text-xs mt-1">{errorMessage}</span>
      )}
    </div>
  );
};
