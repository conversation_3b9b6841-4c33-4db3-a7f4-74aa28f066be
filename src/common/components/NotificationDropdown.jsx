import React, { useState, useEffect, useRef } from "react";

const NotificationDropdown = () => {
    const [isOpen, setIsOpen] = useState(false);

    const notificationsIcons = {
        "payment_request": (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-8 p-1"
            >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
            </svg>
        ),
        "organization_approval": (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-8 p-1"
            >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"
                />
            </svg>
        ),
        "system_alert": (
            <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="size-8 p-1"
            >
                <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                />
            </svg>
        ),
        "envelope": <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-7">
            <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
        </svg>,
        "envelope-open": <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-7">
            <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 9v.906a2.25 2.25 0 0 1-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 0 0 1.183 1.981l6.478 3.488m8.839 2.51-4.66-2.51m0 0-1.023-.55a2.25 2.25 0 0 0-2.134 0l-1.022.55m0 0-4.661 2.51m16.5 1.615a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V8.844a2.25 2.25 0 0 1 1.183-1.981l7.5-4.039a2.25 2.25 0 0 1 2.134 0l7.5 4.039a2.25 2.25 0 0 1 1.183 1.98V19.5Z" />
        </svg>


    };

    // Notifications categorized by type
    const notifications = [
        {
            id: 1,
            type: "payment_request",
            message: "You have a new payment request.",
            time: "2m ago",
            icon: notificationsIcons["payment_request"],
            read: false,
        },
        {
            id: 2,
            type: "organization_approval",
            message: "Your organization verification is pending.",
            time: "5m ago",
            icon: notificationsIcons["organization_approval"],
            read: true,
        },
        {
            id: 4,
            type: "system_alert",
            message: "System maintenance scheduled for tomorrow.",
            time: "1h ago",
            icon: notificationsIcons["system_alert"],
            read: false,
        },
    ];

    // Create a reference for the dropdown
    const dropdownRef = useRef(null);

    // Close the dropdown if clicked outside
    useEffect(() => {
        
        const handleClickOutside = (event) => {
            // console.log(event.target.classList.contains('notificationOpenIcon'))
            if (dropdownRef.current && !dropdownRef.current.contains(event.target) && !event.target.classList.contains('notificationOpenIcon') ) {
                setIsOpen(false);
            }
        };

        // Attach event listener
        document.addEventListener("mousedown", handleClickOutside);

        // Cleanup the event listener on unmount
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div className="relative">
            {/* Notification Icon */}
            <div
                className="notificationOpenIcon relative cursor-pointer select-none"
                onClick={() => setIsOpen(!isOpen)}
            >
                <div className="notificationOpenIcon absolute left-0 top-0 size-5 flex justify-center items-center bg-sky-500 rounded-full">
                    <span className="notificationOpenIcon text-sm text-white p-1 ">{notifications.length}</span>
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-sky-400 opacity-75"></span>
                </div>
                <div className="notificationOpenIcon p-2">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        className="notificationOpenIcon text-sky-100 size-6"
                        viewBox="0 0 16 16"
                    >
                        <path
                            d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zM8 1.918l-.797.161A4.002 4.002 0 0 0 4 6c0 .628-.134 2.197-.459 3.742-.16.767-.376 1.566-.663 2.258h10.244c-.287-.692-.502-1.49-.663-2.258C12.134 8.197 12 6.628 12 6a4.002 4.002 0 0 0-3.203-3.92L8 1.917zM14.22 12c.223.447.481.801.78 1H1c.299-.199.557-.553.78-1C2.68 10.2 3 6.88 3 6c0-2.42 1.72-4.44 4.005-4.901a1 1 0 1 1 1.99 0A5.002 5.002 0 0 1 13 6c0 .88.32 4.2 1.22 6z"
                        />
                    </svg>
                </div>
            </div>

            {/* Notification Dropdown */}
            {isOpen && (
                <div
                    ref={dropdownRef}
                    className="absolute right-0 mt-2 bg-white shadow-lg rounded-lg w-96 max-h-96 overflow-y-auto z-10 border border-lightBlue"
                >
                    <ul className="py-3 px-2">
                        {notifications.map((notification,index) => (
                            <li
                                key={notification.id}
                                className={`cursor-pointer flex items-center space-x-2 p-2 ps-3 hover:bg-blue-100 rounded-md ${index+1!==notifications.length?'border-b':''}`}
                            >
                                <div className="p-2 flex-shrink-0 bg-tranquilBlue text-white rounded-full">
                                    {notification.icon}
                                </div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-tranquilBlue">
                                        {notification.message.slice(0, 30) + "..."}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {notification.time}
                                    </p>
                                </div>
                                <div className="p-2 flex-shrink-0 text-tranquilBlue">
                                    {notification.read?notificationsIcons["envelope-open"]:notificationsIcons["envelope"]}
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default NotificationDropdown;
