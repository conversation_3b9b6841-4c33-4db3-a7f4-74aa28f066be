import React, { useState, type FC } from "react";
import { NavLink } from "react-router";
import {
  Squares2X2Icon,
  DocumentTextIcon,
  BuildingOffice2Icon,
  ChevronDownIcon,
  ChevronRightIcon,
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

type SidebarItem = {
  title: string;
  icon: React.JSX.Element;
  link?: string;
  subItems?: { title: string; link: string }[];
};

const sidebarStructure: SidebarItem[] = [
  {
    title: "Dashboard",
    icon: <Squares2X2Icon className="w-6 h-6" />,
    link: "/",
  },
  {
    title: "Codes",
    icon: <DocumentTextIcon className="w-6 h-6" />,
    subItems: [
      { title: "Subscription Plans", link: "/subscription-plans/add" },
      { title: "Owner", link: "/code/owners" },
      { title: "Subscriptions", link: "/subscriptions" },
    ],
  },

  {
    title: "Accounts",
    icon: <BuildingOffice2Icon className="w-6 h-6" />,
    subItems: [
      { title: "Payment Requests", link: "/accounts/payment-requests" },
      { title: "Verified Org", link: "/accounts/org-verified" },
    ],
  },
];

const Sidebar: FC = () => {
  const [openSection, setOpenSection] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSection = (title: string) => {
    setOpenSection((prev) => (prev === title ? null : title));
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <>
      {/* Mobile toggle button */}
      <button
        onClick={toggleSidebar}
        className="md:hidden fixed top-4 left-4 z-20 p-2 rounded-lg bg-base-200 shadow-md"
        aria-label="Toggle sidebar"
      >
        <Bars3Icon className="w-6 h-6" />
      </button>

      {/* Overlay for mobile */}
      {sidebarOpen && (
        <div
          className="md:hidden fixed inset-0 bg-[rgba(0,0,0,0.1)] bg-opacity-50 z-30"
          onClick={() => {
            setSidebarOpen(false);
          }}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-16 left-0 h-[calc(100vh-4rem)] w-60 bg-base-100 shadow-lg z-40 border-t-0
          transition-all duration-300 ease-in-out overflow-y-auto
          ${sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"}
        `}
      >
        <div className="flex justify-between items-center p-4 md:hidden">
          <span className="font-bold">Menu</span>
          <button
            onClick={() => {
              setSidebarOpen(false);
            }}
            aria-label="Close sidebar"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <ul className="menu p-2 space-y-1">
          {sidebarStructure.map((item) => (
            <li key={item.title}>
              {item.subItems ? (
                <>
                  <button
                    onClick={() => {
                      toggleSection(item.title);
                    }}
                    className="flex items-center justify-between w-full px-4 py-2 rounded-lg hover:bg-base-300"
                  >
                    <div className="flex items-center gap-2 font-medium">
                      {item.icon}
                      {item.title}
                    </div>
                    {openSection === item.title ? (
                      <ChevronDownIcon className="w-5 h-5" />
                    ) : (
                      <ChevronRightIcon className="w-5 h-5" />
                    )}
                  </button>
                  {openSection === item.title && (
                    <ul className="ml-4 mt-1 border-l border-base-300 space-y-1">
                      {item.subItems.map((subItem) => (
                        <li key={subItem.title}>
                          <NavLink
                            to={subItem.link}
                            className="block px-4 py-2 rounded-lg hover:bg-base-300"
                            onClick={() => {
                              if (window.innerWidth < 768) {
                                setSidebarOpen(false);
                              }
                            }}
                          >
                            {subItem.title}
                          </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : (
                <NavLink
                  to={item.link ?? "/"}
                  className="flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-base-300"
                  onClick={() => {
                    if (window.innerWidth < 768) {
                      setSidebarOpen(false);
                    }
                  }}
                >
                  {item.icon}
                  {item.title}
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </aside>
    </>
  );
};

export default Sidebar;
