import { axiosPrivate, axiosPublic } from "@/api/axios";
import { logger } from "@/lib/logger";
import { ApiException } from "@/utils/exceptions";
import type { AxiosError, AxiosRequestConfig } from "axios";
import axios from "axios";

export type FormError = {
  formErrors: string[];
  fieldErrors: Record<string, string[]>;
};
export type ApiError = {
  message: string;
  statusCode: number;
  code: string;
  errors?: FormError;
};

export type ApiRequestConfig = {
  method?: AxiosRequestConfig["method"];
  data?: AxiosRequestConfig["data"];
  params?: AxiosRequestConfig["params"];
  withCredentials?: AxiosRequestConfig["withCredentials"];
  withAuthorization?: boolean;
};

export type ApiResponse<T> = {
  statusCode: number;
  message: string;
  data: T;
};

export async function sendApiRequest<R>(
  url: string,
  options: ApiRequestConfig = {}
) {
  const { withAuthorization, ...axiosOptions } = options;
  const axiosInstance = !withAuthorization ? axiosPublic : axiosPrivate;

  try {
    const response = await axiosInstance<ApiResponse<R>>({
      url,
      ...axiosOptions,
    });
    return response.data.data;
  } catch (error) {
    logger.error(error, "ApiService");
    if (isAxiosError<ApiError>(error) && error.response) {
      const { message, statusCode, code, errors } = error.response.data;
      throw new ApiException(message, statusCode, code, errors);
    } else {
      throw error;
    }
  }
}

function isAxiosError<T>(error: unknown): error is AxiosError<T> {
  return axios.isAxiosError(error);
}
