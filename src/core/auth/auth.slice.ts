import { createSlice, type PayloadAction } from "@reduxjs/toolkit";

export type AuthState = {
  accessToken?: string;
  role?: number;
};

const initialState: AuthState = {};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuth: (state, action: PayloadAction<AuthState>) => {
      state.accessToken = action.payload.accessToken;
      state.role = action.payload.role;
    },
  },
});

export const authActions = authSlice.actions;

export const authReducer = authSlice.reducer;
