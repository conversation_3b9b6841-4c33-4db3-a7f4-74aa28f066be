import React, { useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import {
  UsersIcon,
  BuildingOfficeIcon,
  ClockIcon,
  PlusCircleIcon,
  InboxIcon,
  UserGroupIcon,
  AcademicCapIcon,
  BellIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  ChatBubbleLeftIcon
} from "@heroicons/react/24/outline";

const stats = [
  {
    id: 1,
    title: "Total Students",
    value: "8,901",
    icon: <UsersIcon className="w-6 h-6" />,
  },
  {
    id: 2,
    title: "Total Branches",
    value: "3",
    icon: <BuildingOfficeIcon className="w-6 h-6" />,
  },
  {
    id: 3,
    title: "Active Hours",
    value: "48",
    icon: <ClockIcon className="w-6 h-6" />,
  },
  {
    id: 4,
    title: "Active Sessions",
    value: "124",
    icon: <PlusCircleIcon className="w-6 h-6" />,
  },
  {
    id: 5,
    title: "Number of Requests",
    value: "1,234",
    icon: <InboxIcon className="w-6 h-6" />,
  },
  {
    id: 6,
    title: "Parents Accounts",
    value: "4,567",
    icon: <UserGroupIcon className="w-6 h-6" />,
  },
  {
    id: 7,
    title: "Teachers Accounts",
    value: "1,234",
    icon: <AcademicCapIcon className="w-6 h-6" />,
  },
  {
    id: 8,
    title: "Push Notifications",
    value: "567",
    icon: <BellIcon className="w-6 h-6" />,
  },
  {
    id: 9,
    title: "Emails Sent",
    value: "3,890",
    icon: <EnvelopeIcon className="w-6 h-6" />,
  },
  {
    id: 10,
    title: "WhatsApp Sent",
    value: "2,345",
    icon: <ChatBubbleLeftRightIcon className="w-6 h-6" />,
  },
  {
    id: 11,
    title: "SMS Sent",
    value: "1,678",
    icon: <ChatBubbleLeftIcon className="w-6 h-6" />,
  },
];

const StatsModal = ({ name }) => {
  const [isOpen, setIsOpen] = useState(false);

  const openModal = () => {
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Button to Open Modal */}
      <div className="flex items-center justify-center">
        <button
          className="btn btn-primary"
          onClick={openModal}
        >
          View Stats
        </button>
      </div>

      {/* Modal */}
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={closeModal}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl p-6 overflow-hidden text-left transition-all transform bg-base-100 shadow-xl rounded-2xl">
                  {/* Modal Header */}
                  <div className="flex justify-between items-center mb-6">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-semibold leading-6 text-primary"
                    >
                      {name}
                    </Dialog.Title>
                    <button
                      onClick={closeModal}
                      className="text-base-content hover:text-primary"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={2}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Stats Table */}
                  <div className="overflow-x-auto">
                    <table className="table w-full">
                      <thead>
                        <tr className="text-center">
                          <th className="text-center">Icon</th>
                          <th className="text-center">Title</th>
                          <th className="text-center">Value</th>
                        </tr>
                      </thead>
                      <tbody>
                        {stats.map((stat) => (
                          <tr key={stat.id} className="hover">
                            <td className="text-center">
                              <div className="flex justify-center">
                                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
                                  {stat.icon}
                                </div>
                              </div>
                            </td>
                            <td className="text-center text-base-content/70">{stat.title}</td>
                            <td className="text-center font-semibold text-base-content">{stat.value}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default StatsModal;