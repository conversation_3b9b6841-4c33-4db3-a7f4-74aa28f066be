import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';

function InvoiceModal({ selectedInvoice }) {
    const [isOpen, setIsOpen] = useState(false); // Modal visibility
    const [isApproveClicked, setIsApproveClicked] = useState(false); // Approval button state
    const [isDeclineClicked, setIsDeclineClicked] = useState(false); // Decline button state
    const [declineReason, setDeclineReason] = useState(''); // Decline reason input

    // Handle decline submit
    const handleSubmitDecline = () => {
        console.log('Decline reason submitted:', declineReason);
        setIsDeclineClicked(false);
        setIsOpen(false); // Optionally close the modal after decline
    };

    return (
        <>
            {/* Trigger to open the modal */}
            <a
                className='cursor-pointer hover:underline underline-offset-4 text-tranquilBlue h-full'
                onClick={() => setIsOpen(true)}
            >
                View Invoice
            </a>

            {/* Modal */}
            <Transition appear show={isOpen} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setIsOpen(false)}>
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-gray-900 bg-opacity-50" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex items-center justify-center min-h-full p-4 pb-6">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel
                                    id="printableDiv"
                                    className="w-full max-w-3xl py-2 px-4 overflow-hidden bg-white rounded-lg shadow-xl transform transition-all"
                                    style={{ maxHeight: 'calc(100vh - 5rem)' }}
                                >
                                    {/* Modal Header */}
                                    <div className="flex items-center justify-between pb-4 border-b">
                                        <h3 className="text-xl font-semibold text-tranquilBlue">
                                            Invoice #{selectedInvoice?.id}
                                            <div className="text-sm font-semibold text-gray-500">
                                                {'+923165502120'}
                                            </div>
                                        </h3>
                                        <button
                                            className="text-gray-500 hover:text-gray-800 print:hidden"
                                            onClick={() => setIsOpen(false)}
                                        >
                                            ✕
                                        </button>
                                    </div>

                                    {/* Modal Content */}
                                    <div className="p-0">
                                        {/* Invoice details */}
                                        <div className="flex items-center justify-between pb-4 border-b">
                                            <div className="flex items-center">
                                                <div className="flex items-center justify-center w-12 h-12 text-2xl font-bold text-blue-900 bg-yellow-500 rounded-full">
                                                    10
                                                </div>
                                                <span className="ml-4 text-xl font-semibold">
                                                    Govt. Girls Degree College Qalandarabad, Abbottabad - Online
                                                    College Admission System
                                                </span>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-gray-500">INV1812230002</p>
                                            </div>
                                        </div>

                                        <div class="mt-4 flex justify-between">
                                            <div>
                                                <p class="text-gray-500">Package:</p>
                                                <p class="font-semibold">School Pack Pro (#1439) </p>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-gray-500">Date: <span class="font-semibold">22/11/2023</span></p>
                                                <p class="text-gray-500">Due Date: <span class="font-semibold">24/11/2023</span></p>
                                            </div>
                                        </div>
                                        <div class="mt-6 border-t pt-4">
                                            <div class="flex justify-between">
                                                <div>
                                                    <p class="text-blue-600 font-semibold">FROM:</p>
                                                    <p class="font-semibold">Iqbal Khan <span className='text-gray-500 ps-1'>(Accountant)</span></p>
                                                    <p class="text-gray-500">Chakwal, Punjab, PK</p>
                                                    <p class="text-gray-500">+92 820 4389 2489</p>
                                                    <p class="text-gray-500"><EMAIL></p>
                                                </div>
                                                <div class="text-right">
                                                    <p class="text-blue-600 font-semibold">Package Spec:</p>
                                                    <p class="font-semibold">School Pack Pro (#1439)</p>
                                                    <p class="text-gray-500">30 days</p>
                                                    <p class="text-gray-500">1 Campus</p>
                                                    <p class="text-gray-500">500 students</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-6">
                                            <div class="overflow-x-auto">
                                                <table class="min-w-full border-collapse border border-gray-300">
                                                    <thead class="bg-gray-100">
                                                        <tr>
                                                            <th class="px-4 py-2 border border-gray-300 text-left text-gray-500">Services</th>
                                                            <th class="px-4 py-2 border border-gray-300 text-left text-gray-500">Charges</th>
                                                            <th class="px-4 py-2 border border-gray-300 text-left text-gray-500">Quantity</th>
                                                            <th class="px-4 py-2 border border-gray-300 text-left text-gray-500">Price</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr class="border-b">
                                                            <td class="px-4 py-2 border border-gray-300">Attendance System</td>
                                                            <td class="px-4 py-2 border border-gray-300">3PKR/Student</td>
                                                            <td class="px-4 py-2 border border-gray-300">800</td>
                                                            <td class="px-4 py-2 border border-gray-300">2,400.00PKR</td>
                                                        </tr>
                                                        <tr class="border-b">
                                                            <td class="px-4 py-2 border border-gray-300">Fee Management System</td>
                                                            <td class="px-4 py-2 border border-gray-300">5PKR/Student</td>
                                                            <td class="px-4 py-2 border border-gray-300">800</td>
                                                            <td class="px-4 py-2 border border-gray-300">4,000.00PKR</td>
                                                        </tr>
                                                        <tr class="border-b">
                                                            <td class="px-4 py-2 border border-gray-300">Exam Management System</td>
                                                            <td class="px-4 py-2 border border-gray-300">7PKR/Student</td>
                                                            <td class="px-4 py-2 border border-gray-300">800</td>
                                                            <td class="px-4 py-2 border border-gray-300">5,600.00PKR</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="mt-4">
                                                <div class="flex justify-end">
                                                    <div class="w-1/3">
                                                        <div class="flex justify-between border-b py-2">
                                                            <p>Sub Total</p>
                                                            <p>5,600.00PKR</p>
                                                        </div>
                                                        <div class="flex justify-between border-b py-2">
                                                            <p>Tax (1%)</p>
                                                            <p>56.00PKR</p>
                                                        </div>
                                                        <div class="flex justify-between border-b py-2">
                                                            <p>Discount</p>
                                                            <p>-</p>
                                                        </div>
                                                        <div class="flex justify-between font-semibold text-blue-600 text-lg border-b py-2">
                                                            <p>Balance (PKR)</p>
                                                            <p>5,656.00PKR</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        {/* Approve and Decline Buttons */}
                                        {!isApproveClicked && !isDeclineClicked && (
                                            <div className="flex justify-center pt-4">
                                                <button
                                                    onClick={() => setIsApproveClicked(true)}
                                                    className="px-5 py-2.5 text-sm font-medium text-white bg-green-700 rounded-lg hover:bg-green-800 focus:ring-4 focus:ring-green-300 me-2"
                                                >
                                                    Approve
                                                </button>
                                                <button
                                                    onClick={() => setIsDeclineClicked(true)}
                                                    className="px-5 py-2.5 text-sm font-medium text-white bg-red-700 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300"
                                                >
                                                    Decline
                                                </button>
                                            </div>
                                        )}

                                        {/* Approval Confirmation */}
                                        {isApproveClicked && (
                                            <div className="flex flex-col items-center mt-4">
                                                <label className="flex items-center justify-center w-fit px-4 py-2 text-xl font-semibold text-green-500 border border-dashed border-green-500 rounded-full">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        strokeWidth={1.5}
                                                        stroke="currentColor"
                                                        className="w-6 h-6"
                                                    >
                                                        <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            d="M5 13l4 4L19 7"
                                                        />
                                                    </svg>
                                                    <span className="pl-2">Approved</span>
                                                </label>
                                            </div>
                                        )}

                                        {/* Decline Reason Input */}
                                        {isDeclineClicked && (
                                            <div className="mt-4">
                                                <label className="block text-base font-semibold text-red-400">
                                                    Decline Reason
                                                </label>
                                                <textarea
                                                    className="w-full p-2 mt-1 border border-red-400 rounded outline-none"
                                                    rows="3"
                                                    value={declineReason}
                                                    onChange={(e) => setDeclineReason(e.target.value)}
                                                    placeholder="Enter reason for declining..."
                                                />
                                                <div className="flex justify-between gap-4 mt-3">
                                                    <button
                                                        className="px-4 py-2 text-white bg-gray-500 rounded"
                                                        onClick={() => setIsDeclineClicked(false)}
                                                    >
                                                        Cancel
                                                    </button>
                                                    <button
                                                        className="px-4 py-2 text-white bg-red-500 rounded"
                                                        onClick={handleSubmitDecline}
                                                    >
                                                        Submit Decline
                                                    </button>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </>
    );
}

export default InvoiceModal;
