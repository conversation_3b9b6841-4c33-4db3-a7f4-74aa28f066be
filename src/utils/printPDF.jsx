import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";

/**
 * Downloads or prints a specific HTML element as a PDF.
 *
 * @param {string} elementId - The ID of the element to print.
 * @param {string} fileName - The name of the output PDF file.
 */
export const downloadDivAsPDF = (elementId, fileName = "download.pdf") => {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`Element with ID '${elementId}' not found.`);
        return;
    }

    html2canvas(element).then((canvas) => {
        const imgData = canvas.toDataURL("image/png");
        const pdf = new jsPDF("p", "mm", "a4");

        // Calculate dimensions to fit the PDF page
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

        pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);
        pdf.save(fileName);
    }).catch(err => {
        console.error("Failed to generate PDF", err);
    });
};


export const PrintDiv = (divId) => {
    try {
        // Get the div by its ID
        const divElement = document.getElementById(divId);
        if (!divElement) {
            console.error(`Div with ID '${divId}' not found.`);
            alert("The specified content to print was not found.");
            return;
        }

        // Get the inner HTML content of the div (children only)
        const divContent = divElement.innerHTML;

        // Open a new window for printing
        const printWindow = window.open("", "_blank", "width=800,height=600");
        if (!printWindow) {
            console.error("Failed to open the print window. Ensure pop-ups are allowed.");
            alert("Unable to open the print window. Please check your browser settings.");
            return;
        }

        // Write the content into the print window
        const printDocument = printWindow.document;
        printDocument.open();
        printDocument.write(`
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Print Preview</title>
                <script src="https://cdn.tailwindcss.com"></script>
                <style>
                    /* Add any additional custom styles here if needed */
                    body {
                        margin: 0;
                        font-family: Arial, sans-serif;
                        width: 100vw;
                    }
                </style>
            </head>
            <body>
                ${divContent}
            </body>
            </html>
        `);
        printDocument.close();

        // Ensure the window is fully loaded before printing
        printWindow.onload = () => {
            printWindow.print();
            printWindow.onafterprint = () => {
                printWindow.close();
            };
        };
    } catch (error) {
        console.error("An error occurred while trying to print:", error);
        alert("An unexpected error occurred while attempting to print. Please try again.");
    }
};
