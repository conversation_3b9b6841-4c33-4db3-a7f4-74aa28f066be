import { <PERSON>alog, DialogPanel, DialogTitle } from '@headlessui/react';
import { useState } from 'react';

function PaymentModal({ purchaseID, note, status, amount, proof, refreshPurchasedPackages }) {
  const [isOpen, setIsOpen] = useState(false);
  const [rejectionNote, setRejectionNote] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const userName = localStorage.getItem('email'); // You might want to get this dynamically
  const sessionKey = localStorage.getItem('sessionKey'); // You might want to get this dynamically

  const handleApprove = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('http://**************/EBridge/api/TssEBridge/UpdateReceiptProofVerified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userName,
          sessionKey,
          packagePurchaseID: purchaseID,
        }),
      });

      const result = await response.json();

      if (result.valid) {
        console.log('Payment Approved');
        refreshPurchasedPackages();
        setIsOpen(false);
      } else {
        alert(result.message || 'Error approving payment');
      }
    } catch (error) {
      console.error('Error approving payment:', error);
      alert('An error occurred while approving the payment.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReject = async () => {
    if (rejectionNote === '') {
      alert('Please provide a rejection note.');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('http://**************/EBridge/api/TssEBridge/UpdateReceiptProofReject', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userName,
          sessionKey,
          packagePurchaseID: purchaseID,
          notes: rejectionNote,
        }),
      });

      const result = await response.json();

      if (result.valid) {
        console.log('Payment Rejected:', rejectionNote);
        refreshPurchasedPackages();
        setIsOpen(false);
      } else {
        alert(result.message || 'Error rejecting payment');
      }
    } catch (error) {
      console.error('Error rejecting payment:', error);
      alert('An error occurred while rejecting the payment.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <button className="px-2 py-2 text-tranquilBlue hover:bg-tranquilBlue hover:text-white rounded" onClick={() => setIsOpen(true)}>
        {status !== 'Pending' && <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
  <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
  <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
</svg>
}
      </button>

      <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
        <div className="fixed inset-0 flex w-screen items-center justify-center p-4 bg-gray-800 bg-opacity-50">
          <DialogPanel className="max-w-md w-full space-y-4 border bg-white p-6 rounded-lg shadow-md">
            <DialogTitle className="text-xl text-tranquilBlue font-bold">Payment Proof #{purchaseID}</DialogTitle>

            <span className="text-sm text-gray-600">Price: {amount} PKR</span>

            <div className="">
              <span className="text-sm text-gray-600 mb-2">
                <span className="text-tranquilBlue font-semibold">Note:</span> {note}
              </span>
              <div className="py-4">
                {proof !== "" ? (
                  <img src={proof} onError={(e) => { e.target.classList.add('hidden') }} className="h-96" />
                ) : (
                  <h1 className="text-center text-gray-500">No proof submitted yet</h1>
                )}
              </div>
            </div>

            {/* If rejecting, show rejection note input */}
            {status === 'In Process' && (
              <div className="mt-4">
                <textarea
                  value={rejectionNote}
                  onChange={(e) => setRejectionNote(e.target.value)}
                  placeholder="Provide a rejection note..."
                  rows="2"
                  className="w-full outline-none p-2 border border-gray-300 rounded"
                />
              </div>
            )}

            {status === 'In Process' && (
              <div className="flex justify-center space-x-4 mt-4">
                <button
                  onClick={handleApprove}
                  className="px-4 py-2 bg-green-700 text-white rounded hover:bg-green-600"
                  disabled={isLoading}
                >
                  {isLoading ? 'Approving...' : 'Approve'}
                </button>
                <button
                  onClick={handleReject}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                  disabled={isLoading}
                >
                  {isLoading ? 'Rejecting...' : 'Reject'}
                </button>
              </div>
            )}
          </DialogPanel>
        </div>
      </Dialog>
    </>
  );
}

export default PaymentModal;
