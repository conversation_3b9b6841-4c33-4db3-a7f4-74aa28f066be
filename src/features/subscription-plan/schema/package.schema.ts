import { z } from "zod";

const baseSubscriptionPlanSchema = z.object({
  id: z.string().optional(),

  title: z
    .string()
    .nonempty("Package title is required")
    .min(2, "Package title must be at least 2 characters")
    .max(70, "Package title must not exceed 70 characters"),

  description: z
    .string()
    .max(250, "Description must not exceed 250 characters")
    .optional(),

  price: z
    .number({
      required_error: "Price is required",
      invalid_type_error: "Price must be a number",
    })
    .min(0, "Price cannot be negative"),

  setupCharges: z
    .number({
      required_error: "Setup charges are required",
      invalid_type_error: "Setup charges must be a number",
    })
    .min(0, "Setup charges cannot be negative"),

  branches: z
    .number({
      required_error: "Number of branches is required",
      invalid_type_error: "Branches must be a number",
    })
    .min(1, "At least 1 branch is required")
    .max(10, "Maximum 100 branches allowed"),

  students: z
    .number({
      required_error: "Maximum students is required",
      invalid_type_error: "Students must be a number",
    })
    .min(1, "At least 1 student is required"),

  features: z
    .array(z.string().trim().min(1, "Feature cannot be empty"))
    .default([]),

  annualDiscountPct: z
    .number()
    .min(0, "Discount percentage cannot be negative")
    .max(100, "Discount percentage cannot exceed 100%")
    .optional(),
  createdAt: z.date().optional(),
});

export const createSubscriptionPlanSchema = baseSubscriptionPlanSchema;
