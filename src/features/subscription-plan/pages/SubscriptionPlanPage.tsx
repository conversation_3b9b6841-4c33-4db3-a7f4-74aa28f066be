import { useEffect, useState } from "react";
import { logger } from "@/lib/logger";
import { handleErrorNotification } from "@/utils/exceptions";
import { notify } from "@/lib/notify";
import { FormField } from "@/common/components/ui/form/FormField";
import { useForm } from "react-hook-form";
import { Button } from "@/common/components/ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import type { SubscriptionPlan } from "../types/subscriptionPlans.type";
import { createSubscriptionPlanSchema } from "../schema/package.schema";
import {
  createSubscriptionPlan,
  fetchAllSubscriptionPlans,
} from "../services/subscriptionPlan.service";
import { PricingCard } from "../components/NewPackage";
// Main Package Management Component
export const AddSubscriptionPlanPage = () => {
  const [packages, setPackages] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newFeature, setNewFeature] = useState("");

  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    setValue,
    watch,
    reset,
  } = useForm<SubscriptionPlan>({
    resolver: zodResolver(createSubscriptionPlanSchema),
    defaultValues: {
      title: "",
      description: "",
      price: 0,
      setupCharges: 0,
      branches: 1,
      students: 1,
      features: [],
      annualDiscountPct: 0,
      createdAt: new Date(),
    },
  });

  const formValues = watch();

  // Fetch all packages
  const fetchPackages = async () => {
    setIsLoading(true);
    try {
      const data = await fetchAllSubscriptionPlans();
      setPackages(data as []);
      notify.success("Packages loaded successfully!");
    } catch (error: unknown) {
      logger.error(error);
      handleErrorNotification(error, "Package");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    void fetchPackages();
  }, []);

  // Handle form submission
  const onSubmit = async (data: SubscriptionPlan) => {
    setIsLoading(true);

    try {
      await createSubscriptionPlan(data);
      notify.success("Package created successfully!");
      void fetchPackages();

      // Reset form after successful creation
      reset();
    } catch (error: unknown) {
      logger.error(error);
      handleErrorNotification(error, "Package");
    } finally {
      setIsLoading(false);
    }
  };

  // Feature management
  const handleAddFeature = () => {
    if (!newFeature.trim()) return;

    const currentFeatures = formValues.features || [];
    setValue("features", [...currentFeatures, newFeature.trim()]);
    setNewFeature("");
  };

  const removeFeature = (index: number) => {
    const updatedFeatures = [...(formValues.features || [])];
    updatedFeatures.splice(index, 1);
    setValue("features", updatedFeatures);
  };

  return (
    <div className="min-h-screen py-6 px-4 sm:py-8 sm:px-6 lg:px-8 flex-1">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold">
            Create New Package
          </h1>
          <p className="mt-2 sm:mt-3 max-w-2xl mx-auto text-base sm:text-xl text-base-content/70">
            Configure your software pricing packages for different client needs
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Form Section */}
          <div className="w-full lg:w-2/3 card bg-base-300 shadow-lg">
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="card-body p-4 sm:p-6"
            >
              <div className="grid grid-cols-1 gap-y-4 sm:gap-y-6 gap-x-4 sm:grid-cols-6">
                {/* Package Title */}
                <div className="sm:col-span-3">
                  <FormField
                    name="title"
                    label="Package Title"
                    placeholder="Enterprise Plan"
                    register={register}
                    errorMessage={errors.title?.message}
                  />
                </div>

                {/* Price */}
                <div className="sm:col-span-3">
                  <FormField
                    name="price"
                    label="Monthly Price (PKR)"
                    type="number"
                    placeholder="5000"
                    register={register}
                    valueAsNumber
                    errorMessage={errors.price?.message}
                  />
                </div>

                {/* Setup Charges */}
                <div className="sm:col-span-3">
                  <FormField
                    name="setupCharges"
                    label="Setup Charges (PKR)"
                    type="number"
                    placeholder="10000"
                    register={register}
                    valueAsNumber
                    errorMessage={errors.setupCharges?.message}
                  />
                </div>

                {/* Branches */}
                <div className="sm:col-span-3">
                  <FormField
                    name="branches"
                    label="Number of Branches"
                    type="number"
                    placeholder="3"
                    register={register}
                    valueAsNumber
                    errorMessage={errors.branches?.message}
                  />
                </div>

                {/* Students */}
                <div className="sm:col-span-3">
                  <FormField
                    name="students"
                    label="Maximum Students"
                    type="number"
                    placeholder="1000"
                    register={register}
                    valueAsNumber
                    errorMessage={errors.students?.message}
                  />
                </div>

                {/* Features Management */}
                <div className="sm:col-span-6">
                  <div className="divider mt-2 mb-4">Package Features</div>

                  {/* Add Feature UI */}
                  <div className="flex flex-col sm:flex-row gap-2 mb-3">
                    <div className="flex-grow">
                      <input
                        type="text"
                        value={newFeature}
                        onChange={(e) => {
                          setNewFeature(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleAddFeature();
                          }
                        }}
                        placeholder="Enter a feature"
                        className="input input-bordered w-full"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={handleAddFeature}
                      className="btn btn-primary"
                    >
                      Add Feature
                    </button>
                  </div>

                  {/* Feature List */}
                  <div className="mt-2 flex flex-wrap gap-2">
                    {formValues.features?.map((feature, index) => {
                      if (!feature.trim()) return null;
                      return (
                        <span
                          key={index}
                          className="badge badge-success gap-1 p-3"
                        >
                          {feature.trim()}
                          <button
                            type="button"
                            onClick={() => {
                              removeFeature(index);
                            }}
                            className="ml-1"
                          >
                            <svg
                              className="h-3 w-3"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        </span>
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="mt-6 flex flex-col sm:flex-row justify-end gap-3">
                <Button
                  type="submit"
                  shape="info"
                  pending={isSubmitting || isLoading}
                >
                  {isSubmitting || isLoading
                    ? "Processing..."
                    : "Create Package"}
                </Button>
              </div>
            </form>
          </div>

          {/* Live Preview */}
          <div className="w-full lg:w-1/3">
            <div className="sticky top-8">
              <h3 className="text-lg font-medium mb-4">Preview</h3>
              <PricingCard packageData={formValues} />
            </div>
          </div>
        </div>
        {/* Packages List */}
        {/* Packages Card Grid */}
        <div className="mt-12 sm:mt-16">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h2 className="text-xl sm:text-2xl font-bold">All Packages</h2>
              <p className="mt-2 text-sm opacity-80">
                A list of all the software packages available in your system
              </p>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? (
              <div className="col-span-full flex justify-center items-center py-8">
                <span className="loading loading-spinner loading-lg mr-2"></span>
                Loading packages...
              </div>
            ) : packages.length === 0 ? (
              <div className="col-span-full text-center py-8">
                No packages found. Create your first package above!
              </div>
            ) : (
              packages.map((pkg) => (
                <PricingCard key={pkg.id} packageData={pkg} />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
