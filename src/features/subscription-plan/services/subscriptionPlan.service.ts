/* eslint-disable @typescript-eslint/no-explicit-any */

import { sendApiRequest } from "@/common/services/api.service";
import type { SubscriptionPlan } from "../types/subscriptionPlans.type";

export const fetchAllSubscriptionPlans = async () => {
  const response = await sendApiRequest<SubscriptionPlan[]>(
    "/platform/subscription-plans",
    {
      method: "GET",
      withAuthorization: true,
    }
  );
  return response;
};

export const createSubscriptionPlan = async (data: any) => {
  const response = await sendApiRequest<SubscriptionPlan>(
    "/platform/subscription-plans",
    {
      method: "POST",
      withAuthorization: true,
      data,
    }
  );
  return response;
};
