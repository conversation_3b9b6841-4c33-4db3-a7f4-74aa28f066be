import type { SubscriptionPlan } from "../types/subscriptionPlans.type";

// Package Card Component
interface PricingCardProps {
  packageData: Partial<SubscriptionPlan>;
}

export const PricingCard = ({ packageData }: PricingCardProps) => {
  const { title, price, setupCharges, branches, students, features } =
    packageData;

  // Convert features to array if it's a string
  const featuresList = Array.isArray(features)
    ? features
    : features
      ? String(features)
          .split(",")
          .map((item) => item.trim())
      : [];

  // Add standard features
  const standardFeatures = [
    branches ? `${branches} Branches` : null,
    students ? `${students} Students` : null,
  ].filter(Boolean) as string[];

  const allFeatures = [...standardFeatures, ...featuresList];

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300 w-full h-full">
      <div className="bg-gradient-to-r from-primary to-secondary p-4 sm:p-6 text-primary-content">
        <h3 className="text-lg sm:text-xl font-bold">
          {title ?? "Untitled Package"}
        </h3>
        <div className="mt-3 sm:mt-4 flex items-baseline">
          <span className="text-2xl sm:text-3xl font-extrabold">
            {price ? Number(price).toLocaleString() : 0}
          </span>
          <span className="ml-1 text-base sm:text-xl font-medium">
            PKR/month
          </span>
        </div>
        {setupCharges && Number(setupCharges) > 0 && (
          <p className="mt-1 text-xs sm:text-sm opacity-80">
            + {Number(setupCharges).toLocaleString()} PKR setup fee
          </p>
        )}
      </div>

      <div className="card-body p-4 sm:p-6">
        <ul className="space-y-2 sm:space-y-3">
          {allFeatures.map((feature, index) => (
            <li key={index} className="flex items-start">
              <span className="flex-shrink-0 text-success">
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>
              <span className="ml-2 text-sm sm:text-base">{feature}</span>
            </li>
          ))}
        </ul>

        <button className="btn btn-accent mt-4 sm:mt-6 w-full text-sm sm:text-base">
          Subscribe Now
        </button>
      </div>
    </div>
  );
};
