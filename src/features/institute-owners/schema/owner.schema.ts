import { z } from "zod";

const baseInstituteOwnerSchema = z.object({
  id: z.string().optional(),

  name: z
    .string()
    .nonempty("Full name is required")
    .min(3, "Name must be at least 3 characters")
    .max(70, "Name must not exceed 70 characters"),

  cnic: z.string().nonempty("CNIC is required"),

  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters" })
    .regex(
      /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#+-_=^~\\[\]{}|:;<>,.()]).+/,
      "Password must include uppercase, lowercase, number and special character"
    ),
  gender: z.enum(["MALE", "FEMALE", "OTHER"], {
    message: "Gender is required",
  }),
  email: z
    .string()
    .nonempty("Email is required")
    .email("Invalid email address"),

  phone: z
    .string()
    .nonempty("Phone number is required")
    .min(10, "Phone number must be at least 10 characters")
    .max(20, "Phone number must not exceed 20 characters"),

  address: z
    .string()
    .nonempty("Address is required")
    .min(10, "Address must be at least 10 characters")
    .max(250, "Address must not exceed 250 characters"),

  createdAt: z.date().optional(),
});

export const createInstituteOwnerSchema = baseInstituteOwnerSchema;
