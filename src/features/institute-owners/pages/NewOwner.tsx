/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useState } from "react";
import {
  fetchAllInstituteOwners,
  createOwner,
  deleteOwner,
} from "../services/owner.service";
import { logger } from "@/lib/logger";
import { handleErrorNotification } from "@/utils/exceptions";
import { notify } from "@/lib/notify";
import { FormField } from "@/common/components/ui/form/FormField";
import { useForm } from "react-hook-form";
import { Button } from "@/common/components/ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  IdentificationIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import type { InstituteOwner } from "../types/institute-owner.type";
import { createInstituteOwnerSchema } from "../schema/owner.schema";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { FaEye, FaEyeSlash } from "react-icons/fa";

// Owner Card Component
interface OwnerCardProps {
  ownerData: InstituteOwner;
  onDelete: (id: string) => void;
}

const OwnerCard = ({ ownerData, onDelete }: OwnerCardProps) => {
  const { id, name, cnic, email, phone, address } = ownerData;

  return (
    <div className="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 border-t-4 border-primary w-full overflow-hidden">
      <div className="card-body p-5 sm:p-6">
        <div className="mb-3">
          <h3 className="text-lg sm:text-xl font-bold text-primary">{name}</h3>
          <div className="h-1 w-16 bg-primary/30 mt-1 rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-base-content/90">
          <div className="flex items-center bg-base-200/50 p-2 rounded-lg">
            <span className="font-semibold text-primary mr-2 flex items-center">
              <IdentificationIcon className="h-4 w-4 mr-1" />
              CNIC:
            </span>
            <span className="text-sm">{cnic}</span>
          </div>

          <div className="flex items-center bg-base-200/50 p-2 rounded-lg">
            <span className="font-semibold text-primary mr-2 flex items-center">
              <EnvelopeIcon className="h-4 w-4 mr-1" />
              Email:
            </span>
            <span className="text-sm truncate">{email}</span>
          </div>

          <div className="flex items-center bg-base-200/50 p-2 rounded-lg">
            <span className="font-semibold text-primary mr-2 flex items-center">
              <PhoneIcon className="h-4 w-4 mr-1" />
              Phone:
            </span>
            <span className="text-sm">{phone}</span>
          </div>

          <div className="flex items-center bg-base-200/50 p-2 rounded-lg sm:col-span-2">
            <span className="font-semibold text-primary mr-2 flex items-center">
              <MapPinIcon className="h-4 w-4 mr-1" />
              Address:
            </span>
            <span className="text-sm truncate">{address}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Owner Management Component
const OwnerManagement = () => {
  const [owners, setOwners] = useState<InstituteOwner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    reset,
  } = useForm<InstituteOwner>({
    resolver: zodResolver(createInstituteOwnerSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      cnic: "",
      email: "",
      phone: "",
      address: "",
      createdAt: new Date(),
    },
  });

  // Fetch all owners
  const fetchOwners = async () => {
    setIsLoading(true);
    try {
      const data = await fetchAllInstituteOwners();
      setOwners(data as InstituteOwner[]);
      notify.success("Owners loaded successfully!");
    } catch (error: unknown) {
      logger.error(error);
      handleErrorNotification(error, "InstituteOwner");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    void fetchOwners();
  }, []);

  // Handle form submission
  const onSubmit = async (data: InstituteOwner) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await createOwner(data);
      setSuccess("Owner created successfully!");
      notify.success(
        "Owner added successfully! Credentials have been sent to their email."
      );
      void fetchOwners();

      // Reset form after successful creation
      reset();
    } catch (error: unknown) {
      logger.error(error);
      handleErrorNotification(error, "Owner");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle owner deletion
  const handleDeleteOwner = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this owner?")) {
      setIsLoading(true);
      try {
        await deleteOwner(id);
        notify.success("Owner deleted successfully!");
        void fetchOwners();
      } catch (error: unknown) {
        logger.error(error);
        handleErrorNotification(error, "Owner");
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="min-h-screen py-6 px-4 sm:py-8 sm:px-6 lg:px-8 flex-1">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-extrabold">
            Add New Client
          </h1>
          <p className="mt-2 sm:mt-3 max-w-2xl mx-auto text-base sm:text-xl text-base-content/70">
            Add and manage owners with their personal details
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Form Section */}
          <div className="w-full card bg-base-200 shadow-lg">
            <form
              onSubmit={handleSubmit(onSubmit)}
              className="card-body p-4 sm:p-6"
            >
              <div className="grid grid-cols-1 gap-y-4 sm:gap-y-6 gap-x-4 sm:grid-cols-6">
                {/* Owner Name */}
                <div className="sm:col-span-3">
                  <FormField
                    name="name"
                    label="Full Name"
                    placeholder="John Doe"
                    register={register}
                    errorMessage={errors.name?.message}
                  />
                </div>

                {/* CNIC */}
                <div className="sm:col-span-3">
                  <FormField
                    name="cnic"
                    label="CNIC Number"
                    placeholder="12345-1234567-1"
                    register={register}
                    errorMessage={errors.cnic?.message}
                  />
                </div>

                <div className="sm:col-span-3">
                  <SelectField
                    name="gender"
                    label="Gender"
                    register={register}
                    errorMessage={errors.gender?.message}
                    options={[
                      { value: "MALE", label: "Male" },
                      { value: "FEMALE", label: "Female" },
                      { value: "OTHER", label: "Other" },
                    ]}
                  />
                </div>

                {/* Phone */}
                <div className="sm:col-span-3">
                  <FormField
                    name="phone"
                    label="Phone Number"
                    placeholder="+92 300 1234567"
                    register={register}
                    errorMessage={errors.phone?.message}
                  />
                </div>

                <div className="sm:col-span-3">
                  <FormField
                    name="email"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    register={register}
                    errorMessage={errors.email?.message}
                  />
                </div>

                <div className="sm:col-span-3">
                  <FormField
                    name="password"
                    label="Password"
                    type={showPassword ? "text" : "password"}
                    placeholder="********"
                    register={register}
                    errorMessage={errors.password?.message}
                  >
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-gray-800"
                    >
                      {showPassword ? (
                        <FaEyeSlash size={18} />
                      ) : (
                        <FaEye size={18} />
                      )}
                    </button>
                  </FormField>
                </div>
                {/* Address */}
                <div className="sm:col-span-6">
                  <FormField
                    name="address"
                    label="Full Address"
                    placeholder="123 Main Street, City, Country"
                    register={register}
                    errorMessage={errors.address?.message}
                  />
                </div>
              </div>

              <div className="mt-6 flex flex-col sm:flex-row justify-end gap-3">
                <Button
                  type="submit"
                  shape="info"
                  pending={isSubmitting || isLoading}
                >
                  {isSubmitting || isLoading ? "Processing..." : "Create Owner"}
                </Button>
              </div>
            </form>
          </div>
        </div>

        {/* Owners List */}
        <div className="mt-12 sm:mt-16">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h2 className="text-xl sm:text-2xl font-bold">All Owners</h2>
              <p className="mt-2 text-sm opacity-80">
                A list of all the owners registered in your system
              </p>
            </div>
          </div>

          <div className="mt-4 sm:mt-6 grid gap-4 sm:gap-6 grid-cols-1 lg:grid-cols-2">
            {isLoading ? (
              <div className="col-span-full flex justify-center items-center py-8">
                <span className="loading loading-spinner loading-md mr-2"></span>
                Loading owners...
              </div>
            ) : owners.length === 0 ? (
              <div className="col-span-full text-gray-500 text-center p-4 bg-base-200 rounded-lg">
                <p className="text-md">
                  No owners found. Create your first owner above!
                </p>
              </div>
            ) : (
              owners.map((owner) => (
                <OwnerCard
                  key={owner.id}
                  ownerData={owner}
                  onDelete={handleDeleteOwner}
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OwnerManagement;
