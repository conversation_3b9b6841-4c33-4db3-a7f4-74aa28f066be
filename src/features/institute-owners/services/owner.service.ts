/* eslint-disable @typescript-eslint/no-explicit-any */

import { sendApiRequest } from "@/common/services/api.service";
import type { InstituteOwner } from "../types/institute-owner.type";

export const fetchAllInstituteOwners = async () => {
  const response = await sendApiRequest<InstituteOwner[]>(
    "/sms/institute-owners",
    {
      method: "GET",
      withAuthorization: true,
    }
  );
  return response;
};

export const createOwner = async (data: any) => {
  const response = await sendApiRequest("/sms/institute-owners", {
    method: "POST",
    withAuthorization: true,
    data,
  });
  return response;
};

export const deleteOwner = async (id: string) => {
  const response = await sendApiRequest(`/sms/institute-owners/${id}`, {
    method: "DELETE",
    withAuthorization: true,
  });
  return response;
};
