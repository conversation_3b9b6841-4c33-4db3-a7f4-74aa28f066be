import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { logger } from "@/lib/logger";
import { handleErrorNotification } from "@/utils/exceptions";
import { notify } from "@/lib/notify";
import { FormField } from "@/common/components/ui/form/FormField";
import { SelectField } from "@/common/components/ui/form/SelectField";
import { Button } from "@/common/components/ui/Button";
import { CreditCardIcon, PencilIcon } from "@heroicons/react/24/outline";

import type {
  CreateSubscriptionForm,
  UpdateSubscriptionForm,
  Subscription,
  SubscriptionStatus,
} from "../types/subscription.types";
import {
  createSubscriptionSchema,
  updateSubscriptionSchema,
} from "../schemas/subscription.schema";
import {
  fetchAllSubscriptions,
  createSubscription,
  updateSubscription,
} from "../services/subscription.service";
import type { InstituteOwner } from "@/features/institute-owners/types/institute-owner.type";
import type { SubscriptionPlan } from "@/features/subscription-plan/types/subscriptionPlans.type";
import { fetchAllSubscriptionPlans } from "@/features/subscription-plan/services/subscriptionPlan.service";
import { fetchAllInstituteOwners } from "@/features/institute-owners/services/owner.service";

// Subscription Card Component
interface SubscriptionCardProps {
  subscription: Subscription;
  onUpdate: (subscription: Subscription) => void;
}

const SubscriptionCard = ({
  subscription,
  onUpdate,
}: SubscriptionCardProps) => {
  const {
    status,
    startDate,
    endDate,
    paymentCycle,
    lastPaymentDate,
    plan,
    owner,
    cancellationReason,
  } = subscription;

  const isActive = status === "ACTIVE";
  const statusColor = isActive
    ? "bg-green-100 text-green-800"
    : "bg-red-100 text-red-800";

  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <CreditCardIcon className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {plan.title}
            </h3>
            <span
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColor}`}
            >
              {status}
            </span>
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => {
              onUpdate(subscription);
            }}
            className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
            title="Edit subscription"
          >
            <PencilIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-500">Owner</p>
          <p className="font-medium text-gray-900">{owner.name}</p>
          <p className="text-sm text-gray-600">{owner.email}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Plan Details</p>
          <p className="font-medium text-gray-900">
            ${plan.price}/{paymentCycle.toLowerCase()}
          </p>
          <p className="text-sm text-gray-600">
            {plan.branches} branches, {plan.students} students
          </p>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4 text-sm">
        <div>
          <p className="text-gray-500">Start Date</p>
          <p className="font-medium">
            {new Date(startDate).toLocaleDateString()}
          </p>
        </div>
        <div>
          <p className="text-gray-500">End Date</p>
          <p className="font-medium">
            {new Date(endDate).toLocaleDateString()}
          </p>
        </div>
        <div>
          <p className="text-gray-500">Last Payment</p>
          <p className="font-medium">
            {new Date(lastPaymentDate).toLocaleDateString()}
          </p>
        </div>
      </div>

      {cancellationReason && (
        <div className="mt-4 p-3 bg-red-50 rounded-lg">
          <p className="text-sm text-red-700">
            <strong>Cancellation Reason:</strong> {cancellationReason}
          </p>
        </div>
      )}

      {plan.features.length > 0 && (
        <div className="mt-4">
          <p className="text-sm text-gray-500 mb-2">Features</p>
          <div className="flex flex-wrap gap-1">
            {plan.features.map((feature, index) => (
              <span
                key={index}
                className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Main Subscription Management Component
export const SubscriptionManagementPage = () => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [subscriptionPlans, setSubscriptionPlans] = useState<
    SubscriptionPlan[]
  >([]);
  const [instituteOwners, setInstituteOwners] = useState<InstituteOwner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingSubscription, setEditingSubscription] =
    useState<Subscription | null>(null);
  const [filterStatus, setFilterStatus] = useState<SubscriptionStatus | "ALL">(
    "ALL"
  );

  const isEditMode = editingSubscription !== null;
  const formSchema = isEditMode
    ? updateSubscriptionSchema
    : createSubscriptionSchema;

  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    reset,
    setValue,
  } = useForm<CreateSubscriptionForm | UpdateSubscriptionForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      paymentCycle: "MONTHLY",
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  // Load initial data
  useEffect(() => {
    void loadSubscriptions();
    void loadSubscriptionPlans();
    void loadInstituteOwners();
  }, []);

  const loadSubscriptions = async () => {
    try {
      setIsLoading(true);
      const response = await fetchAllSubscriptions({
        limit: 100,
        ...(filterStatus !== "ALL" && {
          status: filterStatus,
        }),
      });
      setSubscriptions(response.items);
    } catch (error) {
      logger.error("Failed to load subscriptions", error);
      handleErrorNotification(error, "Subscriptions");
    } finally {
      setIsLoading(false);
    }
  };

  const loadSubscriptionPlans = async () => {
    try {
      const plans = await fetchAllSubscriptionPlans();
      setSubscriptionPlans(plans);
    } catch (error) {
      logger.error("Failed to load subscription plans", error);
      handleErrorNotification(error, "Subscription Plans");
    }
  };

  const loadInstituteOwners = async () => {
    try {
      const owners = await fetchAllInstituteOwners();
      setInstituteOwners(owners);
    } catch (error) {
      logger.error("Failed to load institute owners", error);
      handleErrorNotification(error, "Institute Owners");
    }
  };

  const handleFormSubmission = async (
    data: CreateSubscriptionForm | UpdateSubscriptionForm
  ) => {
    try {
      if (isEditMode && editingSubscription) {
        await updateSubscription(
          editingSubscription.id,
          data as UpdateSubscriptionForm
        );
        notify.success("Subscription updated successfully!");
        setEditingSubscription(null);
      } else {
        await createSubscription(data as CreateSubscriptionForm);
        notify.success("Subscription created successfully!");
        setShowCreateForm(false);
      }
      reset();
      await loadSubscriptions();
    } catch (error) {
      logger.error(
        `Failed to ${isEditMode ? "update" : "create"} subscription`,
        error
      );
      handleErrorNotification(error, "Subscription");
    }
  };

  const handleUpdateSubscription = (subscription: Subscription) => {
    setEditingSubscription(subscription);
    setShowCreateForm(false); // Hide create form if open

    // Populate form with existing data
    setValue("planId", subscription.plan.id);
    setValue("paymentCycle", subscription.paymentCycle);
    setValue("endDate", subscription.endDate);
  };

  const filteredSubscriptions = subscriptions.filter(
    (sub) => filterStatus === "ALL" || sub.status === filterStatus
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Subscription Management
          </h1>
          <p className="text-gray-600">Manage institute owner subscriptions</p>
        </div>
        <Button
          onClick={() => {
            if (showCreateForm) {
              setShowCreateForm(false);
            } else {
              setShowCreateForm(true);
              setEditingSubscription(null); // Close edit form if open
            }
            reset();
          }}
          shape="primary"
          className="bg-blue-600 hover:bg-blue-700"
        >
          {showCreateForm ? "Cancel" : "Create New Subscription"}
        </Button>
      </div>

      {/* Filters */}
      <div className="mb-6 flex space-x-4">
        <SelectField
          label="Filter by Status"
          name="status"
          value={filterStatus}
          onChange={(e) => {
            setFilterStatus(e.target.value as SubscriptionStatus | "ALL");
            // Note: You might want to trigger loadSubscriptions() here with the new filter
          }}
          options={[
            { value: "ALL", label: "All Subscriptions" },
            { value: "ACTIVE", label: "Active" },
            { value: "CANCELLED", label: "Cancelled" },
          ]}
        />
      </div>

      {/* Create/Edit Subscription Form */}
      {(showCreateForm || editingSubscription) && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {isEditMode ? "Edit Subscription" : "Create New Subscription"}
          </h2>
          <form
            onSubmit={handleSubmit(handleFormSubmission)}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SelectField
                label="Subscription Plan"
                register={register}
                name="planId"
                errorMessage={errors.planId?.message}
                options={subscriptionPlans.map((plan) => ({
                  value: plan.id,
                  label: `${plan.title} - $${plan.price}`,
                }))}
              />

              {!isEditMode && (
                <SelectField
                  label="Institute Owner"
                  name="ownerId"
                  register={register}
                  errorMessage={(errors as any).ownerId?.message}
                  options={instituteOwners.map((owner) => ({
                    value: owner.id,
                    label: `${owner.name} (${owner.email})`,
                  }))}
                />
              )}

              {isEditMode && (
                <div>
                  <p className="text-sm text-gray-500">Owner</p>
                  <p className="font-medium text-gray-900">
                    {editingSubscription?.owner.name}
                  </p>
                  <p className="text-sm text-gray-600">
                    {editingSubscription?.owner.email}
                  </p>
                </div>
              )}

              <SelectField
                label="Payment Cycle"
                name="paymentCycle"
                register={register}
                errorMessage={errors.paymentCycle?.message}
                options={[
                  { value: "MONTHLY", label: "Monthly" },
                  { value: "YEARLY", label: "Yearly" },
                ]}
              />

              <FormField
                label="End Date"
                type="date"
                name="endDate"
                register={register}
                errorMessage={errors.endDate?.message}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                shape="secondary"
                type="button"
                onClick={() => {
                  if (isEditMode) {
                    setEditingSubscription(null);
                  } else {
                    setShowCreateForm(false);
                  }
                  reset();
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                shape="primary"
                disabled={isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting
                  ? isEditMode
                    ? "Updating..."
                    : "Creating..."
                  : isEditMode
                    ? "Update Subscription"
                    : "Create Subscription"}
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Subscriptions List */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-semibold text-gray-900">
            Subscriptions ({filteredSubscriptions.length})
          </h2>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading subscriptions...</p>
          </div>
        ) : filteredSubscriptions.length === 0 ? (
          <div className="text-center py-8">
            <CreditCardIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No subscriptions found</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredSubscriptions.map((subscription) => (
              <SubscriptionCard
                key={subscription.id}
                subscription={subscription}
                onUpdate={handleUpdateSubscription}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
