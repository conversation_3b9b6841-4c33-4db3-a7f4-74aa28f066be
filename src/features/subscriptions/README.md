# Subscription Management Feature

This feature provides comprehensive subscription management functionality for the EBridge Admin panel.

## Structure

```
subscriptions/
├── components/           # Reusable UI components
│   ├── SubscriptionStatusBadge.tsx
│   ├── SubscriptionFilters.tsx
│   └── index.ts
├── pages/               # Page components
│   └── SubscriptionManagementPage.tsx
├── schemas/             # Zod validation schemas
│   └── subscription.schema.ts
├── services/            # API service functions
│   └── subscription.service.ts
├── types/               # TypeScript type definitions
│   └── subscription.types.ts
├── index.ts             # Feature exports
└── README.md            # This file
```

## Features

### Subscription Management Page (`/subscriptions`)
- **View All Subscriptions**: Display all subscriptions with detailed information
- **Create New Subscription**: Form to create subscriptions for institute owners
- **Filter Subscriptions**: Filter by status (Active/Cancelled) and owner
- **Cancel Subscriptions**: Cancel active subscriptions with reason
- **Update Subscriptions**: Edit subscription details (placeholder for future implementation)

### Components
- **SubscriptionStatusBadge**: Displays subscription status with appropriate styling
- **SubscriptionFilters**: Reusable filter component for subscription lists

### API Integration
- Full CRUD operations for subscriptions
- Integration with subscription plans and institute owners
- Proper error handling and loading states

## Usage

### Importing the Page
```tsx
import { SubscriptionManagementPage } from "@/features/subscriptions";
```

### Using Components
```tsx
import { SubscriptionStatusBadge, SubscriptionFilters } from "@/features/subscriptions";
```

### Using Services
```tsx
import { 
  fetchAllSubscriptions, 
  createSubscription, 
  cancelSubscription 
} from "@/features/subscriptions";
```

## API Endpoints Used

- `GET /subscriptions` - List all subscriptions
- `POST /subscriptions` - Create new subscription
- `GET /subscriptions/:id` - Get subscription by ID
- `PATCH /subscriptions/:id` - Update subscription
- `PATCH /subscriptions/:id/cancel` - Cancel subscription
- `GET /platform/subscription-plans` - Get available plans
- `GET /sms/institute-owners` - Get institute owners

## Types

### Main Types
- `Subscription` - Complete subscription object with plan and owner details
- `CreateSubscriptionForm` - Form data for creating subscriptions
- `UpdateSubscriptionForm` - Form data for updating subscriptions
- `SubscriptionStatus` - "ACTIVE" | "CANCELLED"
- `PaymentCycle` - "MONTHLY" | "YEARLY"

### API Response Types
- `SubscriptionsListResponse` - Paginated list of subscriptions
- `SubscriptionsQueryParams` - Query parameters for filtering

## Validation

All forms use Zod schemas for validation:
- `createSubscriptionSchema` - Validates subscription creation
- `updateSubscriptionSchema` - Validates subscription updates
- `subscriptionResponseSchema` - Validates API responses

## Future Enhancements

1. **Update Modal**: Implement a proper modal for editing subscriptions
2. **Bulk Operations**: Add bulk cancel/update functionality
3. **Advanced Filtering**: Add date range filters, plan-based filtering
4. **Export Functionality**: Export subscription data to CSV/Excel
5. **Subscription Analytics**: Add charts and statistics
6. **Payment Integration**: Connect with payment processing
7. **Notification System**: Email notifications for subscription events
