/* eslint-disable @typescript-eslint/no-explicit-any */

import { sendApiRequest } from "@/common/services/api.service";
import type {
  CreateSubscriptionForm,
  UpdateSubscriptionForm,
  Subscription,
  SubscriptionsListResponse,
  SubscriptionsQueryParams,
} from "../types/subscription.types";

/**
 * Fetch all subscriptions with optional query parameters
 */
export const fetchAllSubscriptions = async (
  params?: SubscriptionsQueryParams
): Promise<SubscriptionsListResponse> => {
  const queryParams = new URLSearchParams();

  if (params?.limit) queryParams.append("limit", params.limit.toString());
  if (params?.offset) queryParams.append("offset", params.offset.toString());
  if (params?.status) queryParams.append("status", params.status);
  if (params?.ownerId) queryParams.append("ownerId", params.ownerId);

  const response = await sendApiRequest<SubscriptionsListResponse>(
    "/subscriptions",
    {
      method: "GET",
      withAuthorization: true,
      params: queryParams,
    }
  );
  return response;
};

/**
 * Fetch a single subscription by ID
 */
export const fetchSubscriptionById = async (
  subscriptionId: string
): Promise<Subscription> => {
  const response = await sendApiRequest<Subscription>(
    `/subscriptions/${subscriptionId}`,
    {
      method: "GET",
      withAuthorization: true,
    }
  );
  return response;
};

/**
 * Create a new subscription
 */
export const createSubscription = async (
  data: CreateSubscriptionForm
): Promise<Subscription> => {
  const response = await sendApiRequest<Subscription>("/subscriptions", {
    method: "POST",
    withAuthorization: true,
    data,
  });
  return response;
};

/**
 * Update an existing subscription
 */
export const updateSubscription = async (
  subscriptionId: string,
  data: UpdateSubscriptionForm
): Promise<Subscription> => {
  const response = await sendApiRequest<Subscription>(
    `/subscriptions/${subscriptionId}`,
    {
      method: "PATCH",
      withAuthorization: true,
      data,
    }
  );
  return response;
};

/**
 * Cancel a subscription
 */
export const cancelSubscription = async (
  subscriptionId: string,
  reason?: string
): Promise<Subscription> => {
  const response = await sendApiRequest<Subscription>(
    `/subscriptions/${subscriptionId}/cancel`,
    {
      method: "PATCH",
      withAuthorization: true,
      data: { reason },
    }
  );
  return response;
};
