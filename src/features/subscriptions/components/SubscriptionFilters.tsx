import { SelectField } from "@/common/components/ui/form/SelectField";
import type { SubscriptionStatus } from "../types/subscription.types";

interface SubscriptionFiltersProps {
  statusFilter: SubscriptionStatus | "ALL";
  onStatusFilterChange: (status: SubscriptionStatus | "ALL") => void;
  ownerFilter?: string;
  onOwnerFilterChange?: (ownerId: string) => void;
  owners?: Array<{ id: string; name: string; email: string }>;
}

export const SubscriptionFilters = ({
  statusFilter,
  onStatusFilterChange,
  ownerFilter,
  onOwnerFilterChange,
  owners = [],
}: SubscriptionFiltersProps) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
      <h3 className="text-sm font-medium text-gray-900 mb-3">Filters</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <SelectField
          label="Status"
          value={statusFilter}
          onChange={(e) => onStatusFilterChange(e.target.value as SubscriptionStatus | "ALL")}
          options={[
            { value: "ALL", label: "All Statuses" },
            { value: "ACTIVE", label: "Active" },
            { value: "CANCELLED", label: "Cancelled" },
          ]}
        />

        {onOwnerFilterChange && (
          <SelectField
            label="Owner"
            value={ownerFilter || ""}
            onChange={(e) => onOwnerFilterChange(e.target.value)}
            options={[
              { value: "", label: "All Owners" },
              ...owners.map(owner => ({
                value: owner.id,
                label: `${owner.name} (${owner.email})`,
              })),
            ]}
          />
        )}
      </div>
    </div>
  );
};
