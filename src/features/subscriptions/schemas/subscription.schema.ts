import { z } from "zod";

// Subscription status enum schema
export const subscriptionStatusSchema = z.enum(["ACTIVE", "CANCELLED"], {
  description: "Current status of the subscription",
});

// Payment cycle enum schema
export const subscriptionPaymentCycleSchema = z.enum(["MONTHLY", "YEARLY"], {
  description: "Payment cycle for the subscription",
});

// UUID validation schema
const uuidSchema = z.string().uuid("Invalid UUID format");

// Subscription plan response schema (for nested plan data)
export const subscriptionPlanResponseSchema = z.object({
  id: uuidSchema,
  title: z.string(),
  description: z.string().nullable(),
  price: z.number(),
  setupCharges: z.number(),
  branches: z.number(),
  students: z.number(),
  features: z.array(z.string()),
});

// Owner response schema (for nested owner data)
export const ownerResponseSchema = z.object({
  id: uuidSchema,
  name: z.string(),
  email: z.string().email(),
});

// Complete subscription response schema
export const subscriptionResponseSchema = z.object({
  id: uuidSchema,
  status: subscriptionStatusSchema,
  startDate: z.coerce.date(),
  paymentCycle: subscriptionPaymentCycleSchema,
  endDate: z.coerce.date(),
  lastPaymentDate: z.coerce.date(),
  cancellationReason: z.string().nullable().optional(),
  createdAt: z.coerce.date(),
  plan: subscriptionPlanResponseSchema,
  owner: ownerResponseSchema,
});

// Create subscription schema (for form validation)
export const createSubscriptionSchema = z.object({
  planId: uuidSchema.describe("ID of the subscription plan being purchased"),
  ownerId: uuidSchema.describe("ID of the institute owner purchasing the subscription"),
  paymentCycle: subscriptionPaymentCycleSchema,
  endDate: z.coerce.date().describe("End date of the subscription period"),
});

// Update subscription schema (for form validation)
export const updateSubscriptionSchema = createSubscriptionSchema
  .omit({
    ownerId: true, // Owner cannot be changed after subscription creation
  })
  .partial();

// Cancel subscription schema
export const cancelSubscriptionSchema = z.object({
  reason: z.string().min(1, "Cancellation reason is required").optional(),
});

// Query parameters schema for listing subscriptions
export const subscriptionsQuerySchema = z.object({
  limit: z.number().min(1).max(100).default(10).optional(),
  offset: z.number().min(0).default(0).optional(),
  status: subscriptionStatusSchema.optional(),
  ownerId: uuidSchema.optional(),
});

// List response schema
export const subscriptionsListResponseSchema = z.object({
  items: z.array(subscriptionResponseSchema),
  total: z.number(),
});
