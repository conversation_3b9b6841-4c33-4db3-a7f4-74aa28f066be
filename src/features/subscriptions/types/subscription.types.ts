import { z } from "zod";
import type {
  createSubscriptionSchema,
  updateSubscriptionSchema,
  subscriptionResponseSchema,
} from "../schemas/subscription.schema";

// Base subscription types from schemas
export type CreateSubscription = z.infer<typeof createSubscriptionSchema>;
export type UpdateSubscription = z.infer<typeof updateSubscriptionSchema>;
export type SubscriptionResponse = z.infer<typeof subscriptionResponseSchema>;

// Subscription status and payment cycle enums
export type SubscriptionStatus = "ACTIVE" | "CANCELLED" | "UNPAID";
export type PaymentCycle = "MONTHLY" | "YEARLY";

// Subscription plan details (nested in subscription response)
export interface SubscriptionPlan {
  id: string;
  title: string;
  description: string | null;
  price: number;
  setupCharges: number;
  branches: number;
  students: number;
  features: string[];
}

// Owner details (nested in subscription response)
export interface SubscriptionOwner {
  id: string;
  name: string;
  email: string;
}

// Complete subscription with all related data
export interface Subscription {
  id: string;
  status: SubscriptionStatus;
  startDate: Date;
  paymentCycle: PaymentCycle;
  endDate: Date;
  lastPaymentDate: Date;
  cancellationReason?: string | null;
  createdAt: Date;
  plan: SubscriptionPlan;
  owner: SubscriptionOwner;
}

// Form data for creating new subscription
export interface CreateSubscriptionForm {
  planId: string;
  ownerId: string;
  paymentCycle: PaymentCycle;
  endDate: Date;
}

// Form data for updating subscription
export interface UpdateSubscriptionForm {
  planId?: string;
  paymentCycle?: PaymentCycle;
  endDate?: Date;
}

// API response types
export interface SubscriptionsListResponse {
  items: Subscription[];
  total: number;
}

// Query parameters for listing subscriptions
export interface SubscriptionsQueryParams {
  limit?: number;
  offset?: number;
  status?: SubscriptionStatus;
  ownerId?: string;
}
