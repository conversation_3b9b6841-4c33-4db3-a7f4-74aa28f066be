import { Toaster } from "react-hot-toast";
import {
  Route,
  createBrowserRouter,
  createRoutesFromElements,
  RouterProvider,
} from "react-router";
import { RootLayout } from "@/common/layouts/rootLayout/RootLayout";
import { AuthLayout } from "@/common/layouts/authLayout/AuthLayout";
import { RequireAuth } from "@/core/auth/components/RequireAuth";
import { PersistentLogin } from "@/core/auth/components/PersistentLogin";
import { ROLES } from "@/common/constants/roles.constants";
import { SignInPage } from "@/core/auth/pages/SigninPage";
import { UnauthorizedPage } from "@/pages/unAuthorized/UnAuthorizedPage";
import Dashboard from "@/pages/dashboard";
import Orgs from "@/pages/orgs";
import PaymentRequests from "@/pages/paymentRequests";
import OwnerManagement from "@/features/institute-owners/pages/NewOwner";
import { AddSubscriptionPlanPage } from "@/features/subscription-plan/pages/SubscriptionPlanPage";
import { SubscriptionManagementPage } from "@/features/subscriptions/pages/SubscriptionManagementPage";

const router = createBrowserRouter(
  createRoutesFromElements([
    // public routes
    <Route path="/login" element={<AuthLayout />}>
      <Route index element={<SignInPage />} />
    </Route>,

    <Route path="/" element={<RootLayout />}>
      <Route element={<UnauthorizedPage />} path="unauthorized" />
      <Route element={<PersistentLogin />}>
        <Route element={<RequireAuth allowedRoles={[ROLES.ADMIN]} />}>
          <Route index element={<Dashboard />} />
          <Route path="code/owners" element={<OwnerManagement />} />
          <Route
            path="subscription-plans/add"
            element={<AddSubscriptionPlanPage />}
          />
          <Route
            path="subscriptions"
            element={<SubscriptionManagementPage />}
          />

          <Route
            path="accounts/payment-requests"
            element={<PaymentRequests />}
          />
          <Route path="accounts/org-verified" element={<Orgs />} />
        </Route>
      </Route>
    </Route>,
  ])
);

export const App = () => {
  return (
    <div>
      <Toaster />
      <RouterProvider router={router} />
    </div>
  );
};
