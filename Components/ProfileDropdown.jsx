import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";

const ProfileDropdown = () => {
    const navigate = useNavigate() 
    const [isOpen, setIsOpen] = useState(false);

    // Profile options
    const options = [
        { id: 1, label: "View Profile", icon: "user" },
        { id: 2, label: "Settings", icon: "settings" },
        {
            id: 3, label: "Log Out", icon: "logout", func: () => {

                const raw = JSON.stringify({
                    username: localStorage.getItem('email'), // Ensure Username and Password are correctly defined
                    sessionKey: localStorage.getItem('sessionKey')
                });
            
                const requestOptions = {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json", // Ensure proper headers
                    },
                    body: raw,
                };

                fetch("http://192.168.100.14/EBridge/api/TssEBridge/Logout", requestOptions)
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json(); // Return the promise for the text
                    })
                    .then((result) => {
                        if (result.valid) {
                            localStorage.clear()
                            navigate('/login')
                        } else {
                            localStorage.clear()
                            navigate('/login')
                        }
                    })
                    .catch((error) => {
                        console.error("Error:", error);
                    });
            }
        }
    ];

    // Create a reference for the dropdown
    const dropdownRef = useRef(null);

    // Close the dropdown if clicked outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };

        // Attach event listener
        document.addEventListener("mousedown", handleClickOutside);

        // Cleanup the event listener on unmount
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const handleProfileClick = () => {
        setIsOpen(!isOpen); // Toggle dropdown visibility
    };

    return (
        <div className="relative">
            {/* Profile Avatar Icon */}
            <div
                className="relative cursor-pointer"
                onClick={handleProfileClick}
            >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-8 text-sky-100">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                </svg>
            </div>

            {/* Profile Dropdown */}
            {isOpen && (
                <div
                    ref={dropdownRef}
                    className="absolute right-0 mt-2 bg-white shadow-lg rounded-lg w-48 z-10 border border-lightBlue"
                >
                    <ul className="p-2">
                        {options.map((option) => (
                            <li
                                onClick={option.func}
                                key={option.id}

                                className="cursor-pointer flex items-center space-x-2 p-2 hover:bg-blue-100 rounded-md"
                            >
                                {/* Icon for the option */}
                                <div className="p-1 text-tranquilBlue rounded-full">
                                    {option.icon === "user" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                        </svg>

                                    )}
                                    {option.icon === "settings" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                        </svg>

                                    )}
                                    {option.icon === "logout" && (
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="m15 15 6-6m0 0-6-6m6 6H9a6 6 0 0 0 0 12h3" />
                                        </svg>

                                    )}
                                </div>

                                {/* Option Label */}
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-tranquilBlue">
                                        {option.label}
                                    </p>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default ProfileDropdown;
